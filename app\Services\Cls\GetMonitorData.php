<?php

/**
 * CLS监控数据获取服务类
 *
 * 负责从腾讯云CLS日志服务获取SDK监控数据，
 * 提供日志查询、数据分析和格式化功能
 *
 * @desc CLS监控数据获取服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/01/15
 * @todo 添加缓存机制优化查询性能，增加数据导出功能，优化应用启动数据查询性能
 * @package App\Services\Cls
 */

namespace App\Services\Cls;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use TencentCloud\Cls\V20201016\ClsClient;
use TencentCloud\Cls\V20201016\Models\SearchLogRequest;
use TencentCloud\Common\Credential;
use TencentCloud\Common\Exception\TencentCloudSDKException;

class GetMonitorData
{
    /**
     * 项目ID到开发者应用ID的映射
     *
     * @var array
     */
    const PROJECT_ID_TO_APP_ID = [
        '11' => 2,      // 闪烁之光
        '107' => 6,     // 长安幻想
        '112' => 15,    // 永夜降临：复苏
        '116' => 13,    // 蔚蓝星球
        '117' => 5,     // 仙凡幻想
        '118' => 46,    // 闪烁之光H5
    ];


    /**
     * 默认查询天数偏移量
     */
    const DEFAULT_DAYS_OFFSET = 15;

    /**
     * 默认返回结果数量限制
     */
    const DEFAULT_RESULT_LIMIT = 7;

    /**
     * 版本字段类型常量
     */
    const VERSION_TYPE_INNER = 'inner_version';
    const VERSION_TYPE_APP = 'app_version';

    /**
     * 启动事件名称常量
     */
    const EVENT_LAUNCH_COMPLETION = 'app_launch_screen_completion';
    const EVENT_LAUNCH_ERROR = 'app_launch_error';

    /**
     * 开始时间
     *
     * @var string
     */
    protected string $startDate;

    /**
     * 结束时间
     *
     * @var string
     */
    protected string $endDate;

    /**
     * 开发者应用ID
     *
     * @var int
     */
    protected int $developerAppId;

    /**
     * CLS配置信息
     *
     * @var array
     */
    protected array $config;

    /**
     * CLS客户端实例
     *
     * @var ClsClient|null
     */
    protected ?ClsClient $client = null;

    /**
     * 系统类型（可选）
     *
     * @var int|null
     */
    protected ?int $osType = null;

    /**
     * 构造函数
     *
     * 初始化CLS监控数据查询的基本参数
     *
     * @param string $startTime 开始时间，格式：Y-m-d H:i:s
     * @param string $endTime 结束时间，格式：Y-m-d H:i:s
     * @param int $developerAppId 开发者应用ID
     * @param int|null $osType 系统类型（可选）
     */
    public function __construct(string $startTime, string $endTime, int $developerAppId, ?int $osType = null)
    {
        $this->startDate = $startTime;
        $this->endDate = $endTime;
        $this->developerAppId = $developerAppId;
        $this->osType = $osType;
        $this->config = config('services.cls')['sdk_monitor'];
    }

    /**
     * 获取监控数据
     *
     * 从腾讯云CLS获取SDK监控数据并进行格式化处理
     *
     * @return array 以event_name为键的日志分析记录
     * @throws \Exception 如果查询失败
     */
    public function getData(): array
    {
        return $this->executeQuery(
            $this->buildQuery(),
            [$this, 'formatRecords'],
            'CLS查询失败',
            '获取CLS日志数据失败'
        );
    }

    /**
     * 执行CLS查询
     *
     * 通用的查询执行方法，处理客户端初始化、查询执行和异常处理
     *
     * @param string $query CLS查询语句
     * @param callable $formatter 数据格式化回调函数
     * @param string $errorLogPrefix 错误日志前缀
     * @param string $exceptionMessage 异常消息前缀
     * @param string|null $customStartTime 自定义开始时间
     * @param string|null $customEndTime 自定义结束时间
     * @return array 格式化后的查询结果
     * @throws \Exception 如果查询失败
     */
    protected function executeQuery(string $query, callable $formatter, string $errorLogPrefix, string $exceptionMessage, ?string $customStartTime = null, ?string $customEndTime = null): array
    {
        try {
            // 获取CLS客户端
            $client = $this->getClsClient();

            // 获取时间戳范围
            [$startTimestamp, $endTimestamp] = $this->getTimestampRange($customStartTime, $customEndTime);

            // 构建搜索请求
            $searchLogRequest = $this->buildSearchRequest($startTimestamp, $endTimestamp, $query);

            // 执行查询
            $result = $client->SearchLog($searchLogRequest);
            $records = $result->getAnalysisRecords();

            // 格式化数据
            return call_user_func($formatter, $records);
        } catch (TencentCloudSDKException $e) {
            $actualStartTime = $customStartTime ?? $this->startDate;
            $actualEndTime = $customEndTime ?? $this->endDate;
            Log::error("{$errorLogPrefix} - 错误信息: {$e->getMessage()}, 开发者应用ID: {$this->developerAppId}, 查询时间范围: {$actualStartTime} 至 {$actualEndTime}");
            throw new \Exception($exceptionMessage . ': ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * 获取CLS客户端实例
     *
     * 单例模式获取CLS客户端，避免重复初始化
     *
     * @return ClsClient CLS客户端实例
     */
    protected function getClsClient(): ClsClient
    {
        if ($this->client === null) {
            $cred = new Credential($this->config['secret_id'], $this->config['secret_key']);
            $this->client = new ClsClient($cred, "ap-shanghai");
        }

        return $this->client;
    }

    /**
     * 获取时间戳范围
     *
     * 将开始时间和结束时间转换为时间戳
     *
     * @param string|null $customStartTime 自定义开始时间
     * @param string|null $customEndTime 自定义结束时间
     * @return array [开始时间戳, 结束时间戳]
     */
    protected function getTimestampRange(?string $customStartTime = null, ?string $customEndTime = null): array
    {
        // 确定实际使用的时间
        $actualStartTime = $customStartTime ?? $this->startDate;
        $actualEndTime = $customEndTime ?? $this->endDate;

        // 直接使用传入的时间，不做额外处理
        $startTimestamp = Carbon::parse($actualStartTime)->timestamp;
        $endTimestamp = Carbon::parse($actualEndTime)->timestamp;

        return [$startTimestamp, $endTimestamp];
    }

    /**
     * 构建搜索请求
     *
     * 创建CLS日志查询请求对象
     *
     * @param int $startTimestamp 开始时间戳
     * @param int $endTimestamp 结束时间戳
     * @param string $query 查询语句
     * @return SearchLogRequest 搜索请求对象
     */
    protected function buildSearchRequest(int $startTimestamp, int $endTimestamp, string $query): SearchLogRequest
    {
        $searchLogRequest = new SearchLogRequest();
        $searchLogRequest->setTopicId($this->config['topic_id']);
        $searchLogRequest->setFrom($startTimestamp * 1000); // 转换为毫秒
        $searchLogRequest->setTo($endTimestamp * 1000); // 转换为毫秒
        $searchLogRequest->setQuery($query);
        $searchLogRequest->setUseNewAnalysis(true);
        $searchLogRequest->setSyntaxRule(1);

        return $searchLogRequest;
    }

    /**
     * 构建基础查询语句
     *
     * 生成CLS基础查询语句
     *
     * @return string 查询语句
     */
    protected function buildQuery(): string
    {
        return $this->buildBaseQuery() . ' | SELECT "event_name", count(*) AS count, count_if("event_name" = \'sdk_pay_complete_delay\' AND "event_value" >= 3) AS "sdk_pay_complete_delay_count", avg("event_value") AS "avg_value", sum("event_value") AS "sum_value" GROUP BY "event_name"';
    }

    /**
     * 构建启动数据查询语句
     *
     * 生成应用启动数据的CLS查询语句
     *
     * @param string $versionField 版本字段名
     * @return string 查询语句
     */
    protected function buildLaunchQuery(string $versionField): string
    {
        $baseQuery = $this->buildBaseQuery();
        $eventFilter = sprintf('event_name:("%s" OR "%s")', self::EVENT_LAUNCH_COMPLETION, self::EVENT_LAUNCH_ERROR);

        $query = "{$baseQuery} AND {$eventFilter}";

        return $query . " | SELECT event_name, {$versionField}, count(*) AS event_count GROUP BY event_name, {$versionField}";
    }

    /**
     * 构建基础查询条件
     *
     * @return string 基础查询条件
     */
    private function buildBaseQuery(): string
    {
        $projectId = array_search($this->developerAppId, self::PROJECT_ID_TO_APP_ID);

        // 构建基础查询条件
        $baseQuery = '';
        if ($projectId === false) {
            $baseQuery = "developer_app_id:{$this->developerAppId}";
        } else {
            // 使用括号确保 OR 条件的优先级
            $baseQuery = "(developer_app_id:{$this->developerAppId} OR project_id:{$projectId})";
        }

        // 添加 os_type 过滤条件
        if ($this->osType !== null) {
            $baseQuery .= " AND os_type:{$this->osType}";
        }

        return $baseQuery;
    }

    /**
     * 解析记录数据
     *
     * 将CLS返回的JSON字符串记录解析为数组
     *
     * @param array $records CLS返回的记录数据
     * @param string $logContext 日志上下文，用于错误日志
     * @return array 解析后的数据数组
     */
    protected function parseRecords(array $records, string $logContext = 'CLS返回数据'): array
    {
        $parsedData = [];

        foreach ($records as $record) {
            $data = json_decode($record, true);
            if ($data) {
                $parsedData[] = $data;
            } else {
                Log::error("{$logContext}格式异常 - 记录内容: {$record}");
            }
        }

        return $parsedData;
    }

    /**
     * 格式化记录数据
     *
     * 将CLS返回的记录数据格式化为数组格式
     *
     * @param array $records CLS返回的记录数据
     * @return array 格式化后的数据，以event_name作为键
     */
    protected function formatRecords(array $records): array
    {
        $formattedData = [];
        $parsedRecords = $this->parseRecords($records);

        foreach ($parsedRecords as $data) {
            if (isset($data['event_name'])) {
                $formattedData[$data['event_name']] = $data;
            }
        }

        return $formattedData;
    }

    /**
     * 获取应用启动监控数据
     *
     * 查询应用启动完成和启动错误事件的统计数据，
     * 按事件名称和内部版本号进行分组统计
     *
     * @return array 按event_name和inner_version分组的启动事件统计数据
     * @throws \Exception 如果查询失败
     */
    public function getAppLaunchData(): array
    {
        return $this->getAppLaunchDataByVersion(self::VERSION_TYPE_INNER);
    }

    /**
     * 按应用版本获取应用启动监控数据
     *
     * 查询应用启动完成和启动错误事件的统计数据，
     * 按事件名称和应用版本号进行分组统计
     *
     * @return array 按event_name和app_version分组的启动事件统计数据
     * @throws \Exception 如果查询失败
     */
    public function getAppLaunchDataByAppVersion(): array
    {
        return $this->getAppLaunchDataByVersion(self::VERSION_TYPE_APP,);
    }

    /**
     * 通用的获取应用启动数据方法
     *
     * @param string $versionField 版本字段名
     * @param array $appVersions 应用版本号数组（可选）
     * @return array 启动事件统计数据
     * @throws \Exception 如果查询失败
     */
    private function getAppLaunchDataByVersion(string $versionField): array
    {
        $extendedTimeRange = $this->getExtendedTimeRange();

        $data = $this->executeQuery(
            $this->buildLaunchQuery($versionField),
            fn($records) => $this->formatAppLaunchRecordsByVersion($records, $versionField),
            'CLS应用启动数据查询失败',
            '获取CLS应用启动数据失败',
            $extendedTimeRange[0],
            $extendedTimeRange[1]
        );

        return $this->processLaunchData($data, $versionField);
    }

    /**
     * 通用的启动数据处理逻辑
     *
     * 按版本分组并计算启动失败率
     *
     * @param array $data 原始数据
     * @param string $versionField 版本字段名
     * @return array 处理后的数据
     */
    private function processLaunchData(array $data, string $versionField): array
    {
        $groupedData = $this->groupDataByVersion($data, $versionField);
        $result = $this->calculateErrorRates($groupedData, $versionField);
        $sortedResult = $this->sortByVersion($result, $versionField);

        return array_slice($sortedResult, 0, self::DEFAULT_RESULT_LIMIT);
    }

    /**
     * 按版本分组数据
     *
     * @param array $data 原始数据
     * @param string $versionField 版本字段名
     * @return array 分组后的数据
     */
    private function groupDataByVersion(array $data, string $versionField): array
    {
        $groupedData = [];
        foreach ($data as $item) {
            $groupedData[$item[$versionField]][$item['event_name']] = $item;
        }
        return $groupedData;
    }

    /**
     * 计算启动失败率
     *
     * @param array $groupedData 分组后的数据
     * @param string $versionField 版本字段名
     * @return array 包含失败率的结果数组
     */
    private function calculateErrorRates(array $groupedData, string $versionField): array
    {
        $result = [];
        foreach ($groupedData as $version => $items) {
            $errorCount = $items[self::EVENT_LAUNCH_ERROR]['event_count'] ?? 0;
            $totalCount = $items[self::EVENT_LAUNCH_COMPLETION]['event_count'] ?? 0;

            $result[] = [
                $versionField => $version,
                'app_launch_error_rate' => $totalCount > 0
                    ? bcadd(round($errorCount / $totalCount * 100, 2), 0, 2)
                    : 0
            ];
        }
        return $result;
    }

    /**
     * 按版本号排序
     *
     * @param array $result 结果数组
     * @param string $versionField 版本字段名
     * @return array 排序后的结果
     */
    private function sortByVersion(array $result, string $versionField): array
    {
        usort($result, function ($a, $b) use ($versionField) {
            $versionA = str_replace('.', '', $a[$versionField]);
            $versionB = str_replace('.', '', $b[$versionField]);
            return (float)$versionB <=> (float)$versionA;
        });

        return $result;
    }

    /**
     * 通用的格式化启动记录数据
     *
     * 将CLS返回的应用启动记录数据格式化为按事件名称和版本号分组的数组格式
     *
     * @param array $records CLS返回的记录数据
     * @param string $versionField 版本字段名
     * @return array 格式化后的数据
     */
    private function formatAppLaunchRecordsByVersion(array $records, string $versionField): array
    {
        $formattedData = [];
        $parsedRecords = $this->parseRecords($records, 'CLS应用启动数据');

        foreach ($parsedRecords as $data) {
            if (isset($data['event_name'], $data[$versionField])) {
                // 使用事件名称和版本号作为复合键
                $key = $data['event_name'] . '_' . $data[$versionField];
                $formattedData[$key] = $data;
            }
        }

        return $formattedData;
    }

    /**
     * 获取扩展的日期范围
     *
     * 将开始时间向前扩展指定天数，以获取更多历史数据
     *
     * @return array 扩展后的日期范围
     */
    protected function getExtendedTimeRange(): array
    {
        $extendedStartTime = Carbon::parse($this->startDate)
            ->subDays(self::DEFAULT_DAYS_OFFSET)
            ->toDateTimeString();

        return [$extendedStartTime, $this->endDate];
    }
}
