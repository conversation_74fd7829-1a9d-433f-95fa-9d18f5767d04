<?php

/**
 * SDK监控仪表板请求验证类
 * @desc 验证SDK监控仪表板接口的请求参数，包括开发者应用ID、开始时间、结束时间和系统类型
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @date 2025-05-29
 */

namespace App\Http\Requests\SdkMonitor;

use App\Http\Requests\Base\BaseFormRequest;
use App\Constants\OsTypeConstants;

class DashboardRequest extends BaseFormRequest
{
    /**
     * 确定用户是否有权提出此请求
     * 
     * 权限校验已移至控制器层面，此处直接返回 true
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'developer_app_id' => [
                'required',
                'integer',
                'min:1'
            ],
            'start_time' => [
                'required',
                'date_format:Y-m-d H:i:s'
            ],
            'end_time' => [
                'required',
                'date_format:Y-m-d H:i:s',
                'after:start_time'
            ],
            'os_type' => [
                'nullable',
                'integer',
                'in:' . implode(',', OsTypeConstants::getValidTypes())
            ]
        ];
    }

    /**
     * 获取验证错误的自定义属性名称
     *
     * @return array<string, string>
     */
    public function attributes()
    {
        return [
            'developer_app_id' => trans('validation.attributes.developer_app_id'),
            'start_time' => trans('validation.attributes.start_time'),
            'end_time' => trans('validation.attributes.end_time'),
            'os_type' => trans('validation.attributes.os_type')
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'developer_app_id.integer' => trans('validation.custom.developer_app_id.integer'),
            'developer_app_id.min' => trans('validation.custom.developer_app_id.min'),
            'start_time.date_format' => trans('validation.custom.start_time.date_format'),
            'end_time.date_format' => trans('validation.custom.end_time.date_format'),
            'end_time.after' => trans('validation.custom.end_time.after'),
            'os_type.integer' => trans('validation.custom.os_type.integer'),
            'os_type.in' => trans('validation.custom.os_type.in')
        ];
    }
}
