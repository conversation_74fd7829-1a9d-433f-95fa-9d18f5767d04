<?php

/**
 * 性能监控数据获取服务类
 *
 * 提供性能监控数据的统计分析功能，包括性能指标统计、图表数据生成等核心功能。
 * 支持按版本分组统计性能数据，包括流畅度、内存使用、网络流量、电池消耗等关键指标。
 *
 * @desc 性能监控数据获取服务类，提供性能指标统计和图表数据生成功能
 * <AUTHOR> chenji<PERSON><EMAIL>
 * @date 2025/05/28
 * @todo 添加缓存机制优化查询性能，增加数据导出功能
 * @package App\Services\PerfMate
 */

namespace App\Services\PerfMate;

use App\Models\PerfMate\ApmGlobalConfig;
use App\Models\PerfMate\PerformanceScoreData;
use Carbon\Carbon;

class GetMonitorData
{
    /**
     * 默认查询天数偏移量
     */
    const DEFAULT_DAYS_OFFSET = 15;

    /**
     * 默认返回结果数量限制
     */
    const DEFAULT_RESULT_LIMIT = 7;

    /**
     * 版本字段类型常量
     */
    const VERSION_TYPE_INNER = 'inner_version';
    const VERSION_TYPE_GAME = 'game_version_code';

    /**
     * 数据库表名常量
     */
    const TABLE_PERFORMANCE_SCORE_DATA = 'performance_score_data';
    const TABLE_PERFORMANCE_STAT_DATA = 'performance_stat_data';
    const TABLE_MYSQL_APM_REPORT_LIST = 'mysql_apm_report_list';
    const TABLE_MYSQL_APM_DEVICE_LIST = 'mysql_apm_device_list';

    /**
     * 开始日期
     *
     * @var string
     */
    protected string $startDate;

    /**
     * 结束日期
     *
     * @var string
     */
    protected string $endDate;

    /**
     * 效能后台ID
     *
     * @var int
     */
    protected int $developerAppId;

    /**
     * 性能分数数据表名
     *
     * @var string
     */
    protected string $performanceScoreDataTable;

    /**
     * 性能统计数据表名
     *
     * @var string
     */
    protected string $performanceStatDataTable;

    /**
     * MySQL APM报告列表表名
     *
     * @var string
     */
    protected string $mysqlApmReportListTable;

    /**
     * MySQL APM设备列表表名
     *
     * @var string
     */
    protected string $mysqlApmDeviceListTable;

    /**
     * 构造函数
     *
     * 初始化性能监控数据查询的基本参数
     *
     * @param string $startDate 开始日期，格式：Y-m-d H:i:s
     * @param string $endDate 结束日期，格式：Y-m-d H:i:s
     * @param int $developerAppId 效能后台应用ID
     */
    public function __construct(string $startDate, string $endDate, int $developerAppId)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->developerAppId = $developerAppId;

        // 初始化表名
        $this->initializeTableNames();
    }

    /**
     * 初始化数据库表名
     *
     * 设置查询中使用的各个数据库表名
     *
     * @return void
     */
    protected function initializeTableNames(): void
    {
        $this->performanceScoreDataTable = self::TABLE_PERFORMANCE_SCORE_DATA;
        $this->performanceStatDataTable = self::TABLE_PERFORMANCE_STAT_DATA;
        $this->mysqlApmReportListTable = self::TABLE_MYSQL_APM_REPORT_LIST;
        $this->mysqlApmDeviceListTable = self::TABLE_MYSQL_APM_DEVICE_LIST;
    }

    /**
     * 获取监控数据
     *
     * 获取性能监控数据的统计数据
     * 注意：图表数据需要通过 getChartDataByInnerVersion() 或 getChartDataByGameVersion() 方法单独获取
     *
     * @return array 包含dbData的数组
     */
    public function getData(): array
    {
        $dateRange = [$this->startDate, $this->endDate];
        $dbData = $this->getDbData($dateRange);

        return $dbData;
    }

    /**
     * 获取最少时长
     *
     * @return int
     */
    protected function getMinDuration(): int
    {
        //获取全局配置
        $config = ApmGlobalConfig::query()->find($this->developerAppId) ?? [];
        return $config['min_duration'] ?? 0;
    }

    /**
     * 获取数据库统计数据
     *
     * 获取性能监控的统计数据，包括流畅度、内存使用、网络流量、电池消耗等指标
     *
     * @param array $dates 日期范围，格式为 [$startDate, $endDate]
     * @return array 包含各项性能指标的统计数据
     */
    protected function getDbData(array $dates): array
    {
        $selectRaw = <<<COLUMNS
round((sum({$this->performanceStatDataTable}.sum_jank_time / {$this->performanceStatDataTable}.sum_frame_times_time) / count({$this->performanceStatDataTable}.session_id)) * 100, 2) as smoothness,
round(sum(({$this->performanceStatDataTable}.max_used_memory / {$this->mysqlApmReportListTable}.app_total_memory) * 100) / count({$this->performanceStatDataTable}.session_id), 2) as avg_memory,
round((sum({$this->performanceStatDataTable}.down_traffic_10 + {$this->performanceStatDataTable}.up_traffic_10) / count({$this->performanceScoreDataTable}.session_id)) / 1024, 2) as avg_network_traffic,
round(sum(case when {$this->performanceStatDataTable}.sum_network_delay > 0 then {$this->performanceStatDataTable}.sum_network_delay / {$this->performanceStatDataTable}.num else 0 end) / sum(case when {$this->performanceStatDataTable}.sum_network_delay > 0 then 1 else 0 end), 2) as avg_network_delay,
round(sum({$this->performanceStatDataTable}.sum_battery_power / {$this->performanceStatDataTable}.num) / count(CASE WHEN {$this->performanceStatDataTable}.sum_battery_power > 0 THEN 1 END), 2)  as avg_battery_power,
round(sum({$this->performanceStatDataTable}.sum_battery_temp / {$this->performanceStatDataTable}.num) / count(CASE WHEN {$this->performanceStatDataTable}.sum_battery_temp > 0 THEN 1 END), 2)  as avg_battery_temp,
round(sum({$this->performanceStatDataTable}.sum_battery_power / {$this->performanceStatDataTable}.sum_fps) / count(CASE WHEN {$this->performanceStatDataTable}.sum_battery_power > 0 THEN 1 END), 2)  as avg_fps_power
COLUMNS;

        $query = PerformanceScoreData::query() // 查询性能分数表
            ->selectRaw($selectRaw) // 计算各项性能指标的平均值
            ->join($this->performanceStatDataTable, "{$this->performanceStatDataTable}.session_id", '=', "{$this->performanceScoreDataTable}.session_id") // 关联性能统计表
            ->join($this->mysqlApmReportListTable, "{$this->performanceStatDataTable}.session_id", '=', "{$this->mysqlApmReportListTable}.id") // 关联性能报告表
            ->join($this->mysqlApmDeviceListTable, function ($join) { // 关联设备表
                return $join->on("{$this->mysqlApmReportListTable}.developer_app_id", '=', "{$this->mysqlApmDeviceListTable}.developer_app_id") // 关联效能后台id
                    ->on("{$this->mysqlApmReportListTable}.dev_str", '=', "{$this->mysqlApmDeviceListTable}.dev_str"); // 关联设备唯一标识
            })
            ->where("{$this->performanceStatDataTable}.duration", '>', $this->getMinDuration()) // 过滤掉小于最小时长的数据
            ->whereBetween("{$this->mysqlApmReportListTable}.created_at", $dates) // 过滤掉不在时间范围内的数据
            ->where("{$this->mysqlApmReportListTable}.developer_app_id", $this->developerAppId) // 只获取当前效能后台Id的数据
            ->where("{$this->mysqlApmDeviceListTable}.is_simulator", 0); // 过滤掉模拟器数据

        $data = $query->firstFromSR();

        // 处理空值，将空值设置为0
        if ($data) {
            foreach ($data as $key => $value) {
                if (empty($value)) {
                    $data[$key] = 0;
                }
            }
        }

        return $data ?: [];
    }

    /**
     * 按内部版本获取折线图数据
     *
     * 按内部版本统计监控指标，包括流畅度等关键性能指标
     *
     * @param array $innerVersions 内部版本号数组
     * @return array 按内部版本分组的监控数据，包含smoothness等指标
     */
    public function getChartDataByInnerVersion(array $innerVersions): array
    {
        if (empty($innerVersions)) {
            return [];
        }

        return array_column($this->getChartDataByVersion(self::VERSION_TYPE_INNER, $innerVersions), null, self::VERSION_TYPE_INNER);
    }

    /**
     * 按游戏版本获取折线图数据
     *
     * 按游戏版本统计监控指标，包括流畅度等关键性能指标
     *
     * @param array $gameVersions 游戏版本代码数组
     * @return array 按游戏版本分组的监控数据，包含smoothness等指标
     */
    public function getChartDataByGameVersion(array $gameVersions): array
    {
        if (empty($gameVersions)) {
            return [];
        }

        return array_column($this->getChartDataByVersion(self::VERSION_TYPE_GAME, $gameVersions), null, self::VERSION_TYPE_GAME);
    }

    /**
     * 通用的按版本获取折线图数据
     *
     * @param string $versionField 版本字段名
     * @param array $versions 版本号数组
     * @return array 按版本分组的监控数据
     */
    private function getChartDataByVersion(string $versionField, array $versions): array
    {
        $extendedDates = $this->getExtendedDateRange();
        $rawData = $this->getPerformanceStatisticsByVersionField($extendedDates, $versionField, $versions);

        return $this->sortAndLimitResultsByVersionField($rawData, $versionField);
    }

    /**
     * 通用的按版本字段获取性能统计数据
     *
     * @param array $dates 日期范围
     * @param string $versionField 版本字段名
     * @param array $versions 版本号数组
     * @return array 性能统计数据
     */
    private function getPerformanceStatisticsByVersionField(array $dates, string $versionField, array $versions): array
    {
        $selectRaw = <<<COLUMNS
{$this->mysqlApmReportListTable}.{$versionField},
round((sum({$this->performanceStatDataTable}.sum_jank_time / {$this->performanceStatDataTable}.sum_frame_times_time) / count({$this->performanceStatDataTable}.session_id)) * 100, 2) as smoothness,
count({$this->performanceStatDataTable}.session_id) as count,
count(distinct {$this->mysqlApmReportListTable}.dev_str) as dev_str_count
COLUMNS;

        return PerformanceScoreData::query() // 查询性能分数表
            ->selectRaw($selectRaw) // 计算各项性能指标的平均值
            ->join($this->performanceStatDataTable, "{$this->performanceStatDataTable}.session_id", '=', "{$this->performanceScoreDataTable}.session_id") // 关联性能统计表
            ->join($this->mysqlApmReportListTable, "{$this->performanceStatDataTable}.session_id", '=', "{$this->mysqlApmReportListTable}.id") // 关联性能报告表
            ->join($this->mysqlApmDeviceListTable, function ($join) { // 关联设备表
                return $join->on("{$this->mysqlApmReportListTable}.developer_app_id", '=', "{$this->mysqlApmDeviceListTable}.developer_app_id") // 关联效能后台id
                    ->on("{$this->mysqlApmReportListTable}.dev_str", '=', "{$this->mysqlApmDeviceListTable}.dev_str"); // 关联设备唯一标识
            })
            ->where("{$this->performanceStatDataTable}.duration", '>', $this->getMinDuration()) // 过滤掉小于最小时长的数据
            ->whereBetween("{$this->mysqlApmReportListTable}.created_at", $dates) // 过滤掉不在时间范围内的数据
            ->where("{$this->mysqlApmReportListTable}.developer_app_id", $this->developerAppId) // 只获取当前效能后台Id的数据
            ->where("{$this->mysqlApmDeviceListTable}.is_simulator", 0) // 过滤掉模拟器数据
            ->where("{$this->mysqlApmReportListTable}.{$versionField}", '!=', '') // 版本字段不为空
            ->whereIn("{$this->mysqlApmReportListTable}.{$versionField}", $versions)
            ->groupBy("{$this->mysqlApmReportListTable}.{$versionField}") // 按版本字段分组
            ->getFromSR();
    }

    /**
     * 通用的按版本字段排序并限制结果数量
     *
     * 对结果按版本号进行数值排序（倒序），并限制返回数量
     *
     * @param array $data 原始数据
     * @param string $versionField 版本字段名
     * @return array 排序并限制后的结果
     */
    private function sortAndLimitResultsByVersionField(array $data, string $versionField): array
    {
        // 简单排序
        usort($data, function ($a, $b) use ($versionField) {
            $versionA = str_replace('.', '', $a[$versionField]);
            $versionB = str_replace('.', '', $b[$versionField]);
            $numA = is_numeric($versionA) ? (float)$versionA : 0;
            $numB = is_numeric($versionB) ? (float)$versionB : 0;
            return $numB <=> $numA; // 倒序排序
        });

        return array_slice($data, 0, self::DEFAULT_RESULT_LIMIT);
    }

    /**
     * 获取扩展的日期范围
     *
     * 将开始时间向前扩展指定天数，以获取更多历史数据
     *
     * @return array 扩展后的日期范围
     */
    protected function getExtendedDateRange(): array
    {
        $extendedStartDate = Carbon::parse($this->startDate)
            ->subDays(self::DEFAULT_DAYS_OFFSET)
            ->toDateTimeString();

        return [$extendedStartDate, $this->endDate];
    }
}
