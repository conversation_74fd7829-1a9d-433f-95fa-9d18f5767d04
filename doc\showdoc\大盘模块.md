# 大盘模块 API 文档

> 作者：陈建权
>
> 日期：2025-06-05
>
> 版本：1.0.0

## 简介

大盘模块提供 SDK 监控数据的综合展示功能，整合版本数据、异常数据、性能数据和 SDK 比率数据，为开发者提供全面的监控大盘。该模块支持缓存机制，提高响应性能。

## API 基础信息

- **基础URL**: `/api`
- **环境**:
  - **正式环境**: `https://sdk-monitor-dashboard.shiyue.com/api`
- **认证方式**: 无需认证
- **响应格式**: JSON
- **统一响应结构**:
  ```json
  {
    "code": 0,       // 业务状态码，0表示成功，其他值表示错误
    "message": "操作成功", // 业务消息，支持国际化
    "data": { ... }    // 业务数据，可能为对象、数组或null
  }
  ```

**正式域名：** `https://sdk-monitor-dashboard.shiyue.com`


**接口地址：** `GET /api/dashboard`


**请求参数：**


| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| developer_app_id | integer | 否 | 6 | 开发者应用ID，必须大于0 |
| start_time | string | 否 | 2025-05-29 00:00:00 | 开始时间，格式：Y-m-d H:i:s |
| end_time | string | 否 | 2025-05-30 00:00:00 | 结束时间，格式：Y-m-d H:i:s，必须晚于开始时间 |


**请求示例：**


```bash
curl -X GET "http://your-domain.com/api/dashboard" \
  -H "Content-Type: application/json" \
  -d '{
    "developer_app_id": 6,
    "start_time": "2025-05-29 00:00:00",
    "end_time": "2025-05-30 00:00:00"
  }'
```


**响应格式：**


```json
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "inner_version": [
      {
        "version": "************",
        "app_launch_error_rate": "0.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      },
      {
        "version": "2025052313",
        "app_launch_error_rate": "75.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      },
      {
        "version": "2024071901",
        "app_launch_error_rate": "0.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      }
    ],
    "app_version": [
      {
        "version": "1.1.1",
        "app_launch_error_rate": "0.00",
        "crash_rate": "0",
        "error_rate": "0",
        "smoothness": "0"
      }
    ],
    "hit_bug_data": {
      "crash": "0.00",
      "error": "0.00",
      "start_count": "0",
      "crash_count": "0",
      "error_count": "0",
      "start_dev_num": "0",
      "crash_dev_num": "0",
      "error_dev_num": "0"
    },
    "perf_mate_data": {
      "smoothness": "0",
      "avg_memory": "0",
      "avg_network_traffic": "0",
      "avg_network_delay": "0",
      "avg_battery_power": "0",
      "avg_battery_temp": "0",
      "avg_fps_power": "0"
    },
    "sdk_data": {
      "app_launch_error": {
        "event_name": "app_launch_error",
        "count": "6",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "app_launch_screen_completion": {
        "event_name": "app_launch_screen_completion",
        "count": "6",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "come_in_game_error": {
        "event_name": "come_in_game_error",
        "count": "4",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "default_server_request_error": {
        "event_name": "default_server_request_error",
        "count": "4",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "download_error": {
        "event_name": "download_error",
        "count": "6",
        "avg_value": "5.00",
        "sum_value": "30"
      },
      "download_status": {
        "event_name": "download_status",
        "count": "4",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "download_time": {
        "event_name": "download_time",
        "count": "6",
        "avg_value": "20.00",
        "sum_value": "120"
      },
      "hot_update_cdn_error": {
        "event_name": "hot_update_cdn_error",
        "count": "6",
        "avg_value": "1.00",
        "sum_value": "6"
      },
      "hot_update_error": {
        "event_name": "hot_update_error",
        "count": "6",
        "avg_value": "3.00",
        "sum_value": "18"
      },
      "hot_update_time_exception": {
        "event_name": "hot_update_time_exception",
        "count": "6",
        "avg_value": "3.00",
        "sum_value": "18"
      },
      "login_game_error": {
        "event_name": "login_game_error",
        "count": "4",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "login_time": {
        "event_name": "login_time",
        "count": "6",
        "avg_value": "5.00",
        "sum_value": "30"
      },
      "login_to_main_screen_time": {
        "event_name": "login_to_main_screen_time",
        "count": "6",
        "avg_value": "2.00",
        "sum_value": "12"
      },
      "network_disconnection": {
        "event_name": "network_disconnection",
        "count": "6",
        "avg_value": "3.00",
        "sum_value": "18"
      },
      "remote_url_error": {
        "event_name": "remote_url_error",
        "count": "4",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "sdk_get_order_error": {
        "event_name": "sdk_get_order_error",
        "count": "6",
        "avg_value": "1.00",
        "sum_value": "6"
      },
      "sdk_get_order_success": {
        "event_name": "sdk_get_order_success",
        "count": "6",
        "avg_value": "2.00",
        "sum_value": "12"
      },
      "sdk_init_error": {
        "event_name": "sdk_init_error",
        "count": "6",
        "avg_value": "2.00",
        "sum_value": "12"
      },
      "sdk_init_success": {
        "event_name": "sdk_init_success",
        "count": "15",
        "avg_value": "1.00",
        "sum_value": "14"
      },
      "sdk_login_error": {
        "event_name": "sdk_login_error",
        "count": "6",
        "avg_value": "1.00",
        "sum_value": "6"
      },
      "sdk_login_success": {
        "event_name": "sdk_login_success",
        "count": "6",
        "avg_value": "1.00",
        "sum_value": "6"
      },
      "sdk_pay_page_time": {
        "event_name": "sdk_pay_page_time",
        "count": "9",
        "avg_value": "54.78",
        "sum_value": "493"
      },
      "sence_change_time": {
        "event_name": "sence_change_time",
        "count": "6",
        "avg_value": "2.00",
        "sum_value": "12"
      },
      "server_request_error": {
        "event_name": "server_request_error",
        "count": "4",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "socket_request_error": {
        "event_name": "socket_request_error",
        "count": "4",
        "avg_value": "0.00",
        "sum_value": "0"
      },
      "ui_open_time": {
        "event_name": "ui_open_time",
        "count": "6",
        "avg_value": "3.00",
        "sum_value": "18"
      }
    },
    "sdk_rate": {
      "average_startup_time": "0.00",
      "app_launch_error_rate": "100.00",
      "hot_update_time": "3.00",
      "hot_update_error_rate": "100.00",
      "sdk_init_error_rate": "28.57",
      "sdk_login_error_rate": "50.00",
      "default_server_request_error_rate": "66.67",
      "server_request_error_rate": "66.67",
      "login_game_error_rate": "66.67",
      "come_in_game_error_rate": "66.67",
      "login_to_main_screen_time": "2.00",
      "download_error_rate": "100.00",
      "ui_open_time": "3.00",
      "scene_change_time": "2.00",
      "network_disconnection": "1.00",
      "order_failure_rate": "15.50",
      "pay_call_duration": "2.30",
      "black_product_interception_rate": "5.20",
      "payment_failure_rate": "8.75",
      "account_delay_rate": "12.40",
      "recharge_delivery_failure_rate": "3.60"
    }
  }
}
```


**错误响应：**


```json
{
  "code": 422,
  "message": "验证失败：开发者应用ID必须是整数",
  "data": null
}
```


## 数据字段说明


### inner_version 字段说明


- `version`: 内部版本号（字符串格式，如：************）
- `app_launch_error_rate`: 应用启动错误率（字符串格式，保留两位小数）
- `crash_rate`: 崩溃率（字符串格式）
- `error_rate`: 错误率（字符串格式）
- `smoothness`: 卡顿率（字符串格式）


### app_version 字段说明


- `version`: 应用版本号（字符串格式，如：1.1.1）
- `app_launch_error_rate`: 应用启动错误率（字符串格式，保留两位小数）
- `crash_rate`: 崩溃率（字符串格式）
- `error_rate`: 错误率（字符串格式）
- `smoothness`: 卡顿率（字符串格式）


### hit_bug_data 字段说明


- `crash`: 崩溃率（字符串格式，保留两位小数）
- `error`: 错误率（字符串格式，保留两位小数）
- `start_count`: 启动次数（字符串格式）
- `crash_count`: 崩溃次数（字符串格式）
- `error_count`: 错误次数（字符串格式）
- `start_dev_num`: 启动设备数（字符串格式）
- `crash_dev_num`: 崩溃设备数（字符串格式）
- `error_dev_num`: 错误设备数（字符串格式）


### perf_mate_data 字段说明


- `smoothness`: 卡顿率（字符串格式，保留两位小数）
- `avg_memory`: 平均内存使用（字符串格式，保留两位小数，单位：MB）
- `avg_network_traffic`: 平均网络流量（字符串格式，保留两位小数，单位：KB）
- `avg_network_delay`: 平均网络延迟（字符串格式，保留两位小数，单位：ms）
- `avg_battery_power`: 平均电池电量（字符串格式，保留两位小数，单位：%）
- `avg_battery_temp`: 平均电池温度（字符串格式，保留两位小数，单位：℃）
- `avg_fps_power`: 每帧功耗（字符串格式，保留两位小数，单位：mW）


### sdk_data 字段说明


SDK事件数据，包含各种事件的统计信息，每个事件包含以下字段：


- `event_name`: 事件名称（字符串）
- `count`: 事件发生次数（字符串格式）
- `avg_value`: 平均值（字符串格式，保留两位小数）
- `sum_value`: 总值（字符串格式）


**支持的事件类型：**
- `app_launch_error`: 应用启动错误
- `app_launch_screen_completion`: 应用启动屏幕完成
- `come_in_game_error`: 进入游戏错误
- `default_server_request_error`: 默认服务器请求错误
- `download_error`: 下载错误
- `download_status`: 下载状态
- `download_time`: 下载时间
- `hot_update_cdn_error`: 热更新CDN错误
- `hot_update_error`: 热更新错误
- `hot_update_time_exception`: 热更新时间异常
- `login_game_error`: 登录游戏错误
- `login_time`: 登录时间
- `login_to_main_screen_time`: 登录到主界面时间
- `network_disconnection`: 网络断开
- `remote_url_error`: 远程URL错误
- `sdk_get_order_error`: SDK获取订单错误
- `sdk_get_order_success`: SDK获取订单成功
- `sdk_init_error`: SDK初始化错误
- `sdk_init_success`: SDK初始化成功
- `sdk_login_error`: SDK登录错误
- `sdk_login_success`: SDK登录成功
- `sdk_pay_page_time`: SDK支付页面时间
- `sdk_pay_black`: SDK支付黑产拦截
- `sdk_pay_times`: SDK支付次数
- `sdk_pay_fail`: SDK支付失败
- `sdk_pay_success`: SDK支付成功
- `sdk_pay_complete_delay`: SDK支付完成延迟
- `sdk_pay_complete_error`: SDK支付完成错误
- `sdk_pay_complete_success`: SDK支付完成成功
- `sence_change_time`: 场景切换时间
- `server_request_error`: 服务器请求错误
- `socket_request_error`: Socket请求错误
- `ui_open_time`: UI打开时间


### sdk_rate 字段说明


- `average_startup_time`: 平均启动时间（字符串格式，保留两位小数，单位：秒）
- `app_launch_error_rate`: 应用启动错误率（字符串格式，保留两位小数）
- `hot_update_time`: 热更新速度（字符串格式，保留两位小数，单位：秒）
- `hot_update_error_rate`: 热更新错误率（字符串格式，保留两位小数）
- `sdk_init_error_rate`: SDK初始化错误率（字符串格式，保留两位小数）
- `sdk_login_error_rate`: SDK登录错误率（字符串格式，保留两位小数）
- `default_server_request_error_rate`: 默认服务器请求错误率（字符串格式，保留两位小数）
- `server_request_error_rate`: 服务器请求错误率（字符串格式，保留两位小数）
- `login_game_error_rate`: 登录游戏错误率（字符串格式，保留两位小数）
- `come_in_game_error_rate`: 进入游戏错误率（字符串格式，保留两位小数）
- `login_to_main_screen_time`: 登录到主界面时间（字符串格式，保留两位小数，单位：秒）
- `download_error_rate`: 下载错误率（字符串格式，保留两位小数）
- `ui_open_time`: UI打开时间（字符串格式，保留两位小数）
- `scene_change_time`: 场景切换时间（字符串格式，保留两位小数）
- `network_disconnection`: 网络断开次数（字符串格式，保留两位小数）
- `order_failure_rate`: 订单失败率（字符串格式，保留两位小数，单位：%）
- `pay_call_duration`: 支付调用耗时（字符串格式，保留两位小数，单位：秒）
- `black_product_interception_rate`: 黑产拦截率（字符串格式，保留两位小数，单位：%）
- `payment_failure_rate`: 支付失败率（字符串格式，保留两位小数，单位：%）
- `account_delay_rate`: 到账延率（字符串格式，保留两位小数，单位：%）
- `recharge_delivery_failure_rate`: 充值发货失败率（字符串格式，保留两位小数，单位：%）


## 状态码说明


- `0`: 成功
- `422`: 验证失败
- `500`: 服务器错误


## 注意事项


1. 所有时间参数必须使用 `Y-m-d H:i:s` 格式
2. 结束时间必须晚于开始时间
3. 开发者应用ID必须是大于0的整数
4. 所有数值字段都会被格式化为字符串类型，数值保留两位小数
5. 内部版本号采用时间戳格式（如：************），应用版本号采用语义化版本格式（如：1.1.1）
6. `inner_version` 数组包含多个内部版本的数据，每个对象包含 `version` 字段
7. `app_version` 数组包含多个应用版本的数据，每个对象包含 `version` 字段
8. `sdk_data` 包含33种不同事件类型的详细统计信息
9. `sdk_rate` 包含21个关键性能指标的计算结果
10. 响应遵循项目统一的API响应规范
11. `perf_mate_data` 包含7个性能监控指标，包括网络延迟、电池电量、电池温度和每帧功耗等新增字段